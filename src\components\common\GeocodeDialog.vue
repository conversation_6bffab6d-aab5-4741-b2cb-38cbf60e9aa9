<template>
  <div v-if="visible" class="dialog-overlay" @click="closeDialog">
    <div class="dialog-container" @click.stop>
      <!-- 对话框头部 -->
      <div class="dialog-header">
        <div class="title-wrapper">
          <div class="title-content">
            <h3>批量查询写字楼经纬度</h3>
            <button class="close-btn" @click="closeDialog">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 对话框内容 -->
      <div class="dialog-content">
        <!-- 输入区域 -->
        <div class="input-section">
          <div class="input-label">请输入写字楼名称（每行一个）：</div>
          <textarea
            v-model="buildingNames"
            class="building-input"
            placeholder="例如：&#10;德思勤城市广场&#10;华尔街中心&#10;华远国际中心"
            rows="8"
          ></textarea>
          <div class="input-actions">
            <button class="btn btn-primary" @click="startBatchQuery" :disabled="isQuerying">
              <span v-if="isQuerying">查询中...</span>
              <span v-else>开始批量查询</span>
            </button>
            <button class="btn btn-secondary" @click="clearResults">清空结果</button>
            <button class="btn btn-info" @click="loadSampleData">加载示例数据</button>
          </div>
        </div>

        <!-- 进度显示 -->
        <div v-if="isQuerying" class="progress-section">
          <div class="progress-info">
            <span>查询进度：{{ queryProgress.current }} / {{ queryProgress.total }}</span>
            <span>{{ Math.round((queryProgress.current / queryProgress.total) * 100) }}%</span>
          </div>
          <div v-if="currentQueryBuilding" class="current-query">
            正在查询：{{ currentQueryBuilding }}
          </div>
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: (queryProgress.current / queryProgress.total) * 100 + '%' }"
            ></div>
          </div>
        </div>

        <!-- 结果显示 -->
        <div v-if="results.length > 0" class="results-section">
          <div class="results-header">
            <h4>查询结果 ({{ results.length }} 条)</h4>
            <div class="results-actions">
              <button class="btn btn-success" @click="exportResults">导出结果</button>
              <button class="btn btn-info" @click="copyResults">复制结果</button>
            </div>
          </div>
          <div class="results-table">
            <div class="table-header">
              <div class="col-name">写字楼名称</div>
              <div class="col-street">街道</div>
              <div class="col-lng">经度</div>
              <div class="col-lat">纬度</div>
              <div class="col-status">状态</div>
            </div>
            <div class="table-body">
              <div
                v-for="(result, index) in results"
                :key="index"
                class="table-row"
                :class="{ 'success': result.success, 'error': !result.success }"
              >
                <div class="col-name">{{ result.name }}</div>
                <div class="col-street">{{ result.street || '-' }}</div>
                <div class="col-lng">{{ result.longitude || '-' }}</div>
                <div class="col-lat">{{ result.latitude || '-' }}</div>
                <div class="col-status">
                  <span v-if="result.success" class="status-success">✓ 成功</span>
                  <span v-else class="status-error">✗ 失败</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { geocodeBuilding } from '@/net/addressHooks.js';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close']);

// 数据
const buildingNames = ref('');
const isQuerying = ref(false);
const results = ref([]);
const currentQueryBuilding = ref(''); // 当前正在查询的建筑名称
const queryProgress = reactive({
  current: 0,
  total: 0
});

// 关闭对话框
const closeDialog = () => {
  emit('close');
};

// 开始批量查询
const startBatchQuery = async () => {
  const names = buildingNames.value
    .split('\n')
    .map(name => name.trim())
    .filter(name => name.length > 0);

  if (names.length === 0) {
    alert('请输入至少一个写字楼名称');
    return;
  }

  isQuerying.value = true;
  results.value = [];
  queryProgress.current = 0;
  queryProgress.total = names.length;

  for (let i = 0; i < names.length; i++) {
    const name = names[i];
    currentQueryBuilding.value = name; // 更新当前查询的建筑名称
    console.log(`正在查询第 ${i + 1} 个写字楼: ${name}`);

    try {
      const coordinates = await geocodeBuilding(name);
      
      if (coordinates) {
        results.value.push({
          name,
          longitude: coordinates.longitude,
          latitude: coordinates.latitude,
          street: coordinates.street,
          district: coordinates.district,
          city: coordinates.city,
          formatted_address: coordinates.formatted_address,
          success: true
        });
      } else {
        results.value.push({
          name,
          longitude: null,
          latitude: null,
          street: null,
          district: null,
          city: null,
          formatted_address: null,
          success: false
        });
      }
    } catch (error) {
      console.error(`查询 ${name} 失败:`, error);
      results.value.push({
        name,
        longitude: null,
        latitude: null,
        street: null,
        district: null,
        city: null,
        formatted_address: null,
        success: false
      });
    }

    queryProgress.current = i + 1;

    // 增加延迟时间，降低查询速度以提高成功率
    if (i < names.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1200)); // 进一步增加到1200ms
    }
  }

  isQuerying.value = false;
  currentQueryBuilding.value = ''; // 清空当前查询状态
  console.log('批量查询完成，结果:', results.value);
};

// 清空结果
const clearResults = () => {
  results.value = [];
  buildingNames.value = '';
  queryProgress.current = 0;
  queryProgress.total = 0;
};

// 导出结果
const exportResults = () => {
  const successResults = results.value.filter(r => r.success);
  if (successResults.length === 0) {
    alert('没有成功的查询结果可导出');
    return;
  }

  const jsonData = successResults.map(r => ({
    name: r.name,
    longitude: r.longitude,
    latitude: r.latitude,
    street: r.street,
    district: r.district,
    city: r.city,
    formatted_address: r.formatted_address
  }));

  const dataStr = JSON.stringify(jsonData, null, 2);
  const blob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `写字楼坐标_${new Date().toISOString().slice(0, 10)}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// 复制结果
const copyResults = () => {
  const successResults = results.value.filter(r => r.success);
  if (successResults.length === 0) {
    alert('没有成功的查询结果可复制');
    return;
  }

  const textData = successResults
    .map(r => `${r.name}\t${r.street || ''}\t${r.longitude}\t${r.latitude}\t${r.formatted_address || ''}`)
    .join('\n');

  navigator.clipboard.writeText(textData).then(() => {
    alert('结果已复制到剪贴板');
  }).catch(err => {
    console.error('复制失败:', err);
    alert('复制失败，请手动复制');
  });
};

// 加载示例数据
const loadSampleData = () => {
  buildingNames.value = `德思勤城市广场
华尔街中心
华远国际中心
汇景发展环球中心
长沙佳兆业广场
长沙绿地中心T1栋
润和金融中心
湖南商会大厦
万坤图财富广场
新湖南大厦`;
};
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(5px);
}

.dialog-container {
  background: linear-gradient(135deg, rgba(6, 30, 93, 0.95) 0%, rgba(6, 30, 93, 0.98) 100%);
  border-radius: 12px;
  border: 1px solid rgba(79, 195, 247, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  width: 90vw;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  padding: 20px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.title-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title-content h3 {
  color: #4fc3f7;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #4fc3f7;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
  width: 24px;
  height: 24px;
}

.close-btn:hover {
  background: rgba(79, 195, 247, 0.1);
  color: white;
}

.dialog-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-label {
  color: #4fc3f7;
  font-size: 14px;
  font-weight: bold;
}

.building-input {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 8px;
  background: rgba(0, 20, 40, 0.5);
  color: white;
  font-size: 14px;
  resize: vertical;
  min-height: 120px;
}

.building-input:focus {
  outline: none;
  border-color: #4fc3f7;
  box-shadow: 0 0 10px rgba(79, 195, 247, 0.3);
}

.input-actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #4fc3f7, #29b6f6);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #29b6f6, #03a9f4);
  transform: translateY(-1px);
}

.btn-secondary {
  background: rgba(79, 195, 247, 0.2);
  color: #4fc3f7;
  border: 1px solid rgba(79, 195, 247, 0.3);
}

.btn-secondary:hover {
  background: rgba(79, 195, 247, 0.3);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #4caf50, #45a049);
  color: white;
}

.btn-info {
  background: linear-gradient(135deg, #2196f3, #1976d2);
  color: white;
}

.progress-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  color: #4fc3f7;
  font-size: 14px;
}

.current-query {
  color: #29b6f6;
  font-size: 12px;
  margin: 5px 0;
  font-style: italic;
}

.progress-bar {
  height: 8px;
  background: rgba(79, 195, 247, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4fc3f7, #29b6f6);
  transition: width 0.3s ease;
}

.results-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-header h4 {
  color: #4fc3f7;
  margin: 0;
}

.results-actions {
  display: flex;
  gap: 10px;
}

.results-table {
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1fr 0.8fr;
  background: rgba(79, 195, 247, 0.1);
  padding: 12px;
  font-weight: bold;
  color: #4fc3f7;
  border-bottom: 1px solid rgba(79, 195, 247, 0.3);
}

.table-body {
  max-height: 300px;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1fr 0.8fr;
  padding: 10px 12px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.1);
  color: white;
  transition: background 0.3s ease;
}

.table-row:hover {
  background: rgba(79, 195, 247, 0.05);
}

.table-row.success {
  border-left: 3px solid #4caf50;
}

.table-row.error {
  border-left: 3px solid #f44336;
}

.status-success {
  color: #4caf50;
}

.status-error {
  color: #f44336;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dialog-container {
    width: 95vw;
    max-height: 95vh;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1.2fr 1fr 0.8fr 0.8fr 0.6fr;
    font-size: 12px;
  }
  
  .input-actions,
  .results-actions {
    flex-direction: column;
  }
}
</style>
