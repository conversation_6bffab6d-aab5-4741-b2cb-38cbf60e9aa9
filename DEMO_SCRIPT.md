# 写字楼经纬度批量查询功能演示脚本

## 演示步骤

### 1. 功能介绍
- 打开应用主页面 `http://localhost:8081/`
- 指出页面右下角的蓝色悬浮按钮
- 说明：这是一个批量查询写字楼经纬度的工具

### 2. 打开查询工具
- 点击右下角的悬浮按钮
- 弹窗出现，展示查询界面
- 介绍界面各个部分：
  - 输入区域：用于输入写字楼名称
  - 操作按钮：开始查询、清空结果、加载示例数据
  - 结果显示区域：显示查询进度和结果

### 3. 加载示例数据
- 点击"加载示例数据"按钮
- 文本框自动填入10个长沙写字楼名称：
  ```
  德思勤城市广场
  华尔街中心
  华远国际中心
  汇景发展环球中心
  长沙佳兆业广场
  长沙绿地中心T1栋
  润和金融中心
  湖南商会大厦
  万坤图财富广场
  新湖南大厦
  ```

### 4. 开始批量查询
- 点击"开始批量查询"按钮
- 观察进度条实时更新
- 显示查询进度：1/10, 2/10, ... 10/10
- 显示百分比：10%, 20%, ... 100%

### 5. 查看查询结果
- 查询完成后，结果表格显示：
  - 写字楼名称
  - 经度坐标
  - 纬度坐标
  - 查询状态（成功/失败）
- 成功的项目显示绿色边框
- 失败的项目显示红色边框

### 6. 导出结果
- 点击"导出结果"按钮
- 下载JSON格式文件，包含所有成功查询的坐标数据
- 文件名格式：`写字楼坐标_2024-08-14.json`

### 7. 复制结果
- 点击"复制结果"按钮
- 数据以制表符分隔格式复制到剪贴板
- 可以直接粘贴到Excel或其他表格软件

### 8. 手动输入测试
- 点击"清空结果"重置
- 手动输入一个写字楼名称，如："长沙国金中心"
- 点击查询，验证单个查询功能

### 9. 关闭工具
- 点击弹窗右上角的关闭按钮
- 或点击弹窗外部区域关闭

## 技术亮点展示

### 1. 实时进度显示
- 展示查询过程中的实时进度更新
- 进度条动画效果

### 2. 错误处理
- 输入不存在的写字楼名称
- 展示查询失败的处理方式

### 3. 响应式设计
- 调整浏览器窗口大小
- 展示在不同屏幕尺寸下的适配效果

### 4. 动画效果
- 悬浮按钮的脉冲动画
- 悬停时的放大效果
- 弹窗的淡入淡出效果

## 使用场景说明

### 1. 数据收集
- 房地产公司收集写字楼位置信息
- 城市规划部门统计商业建筑分布
- 物流公司规划配送路线

### 2. 地图标注
- 在地图上批量标注写字楼位置
- 制作商业地产分布图
- 分析区域商业密度

### 3. 数据分析
- 分析写字楼地理分布特征
- 计算区域中心点
- 进行空间数据分析

## 技术特点

### 1. 高德API集成
- 使用高德地图地理编码API
- 支持地址+建筑名称的精确查询
- 自动fallback到城市+建筑名称

### 2. 批量处理
- 支持一次性查询多个地址
- 自动限流避免API限制
- 实时显示查询进度

### 3. 数据导出
- JSON格式导出，便于程序处理
- 剪贴板复制，便于手动使用
- 支持成功/失败状态筛选

### 4. 用户体验
- 直观的界面设计
- 实时反馈和进度显示
- 响应式设计适配多设备

## 扩展可能性

### 1. 功能扩展
- 支持Excel/CSV文件导入
- 添加地图预览功能
- 支持逆地理编码（坐标转地址）

### 2. API扩展
- 支持百度地图API
- 支持腾讯地图API
- 支持多API源对比验证

### 3. 数据处理
- 添加坐标系转换功能
- 支持批量距离计算
- 添加地理围栏功能
