// 测试高德API的POI搜索、地理编码和逆地理编码
export const testGeocodeAPI = async () => {
  const AMAP_KEY = '81ea7b2440e2bddd9f6b540fa04c141d';
  const testBuilding = '德思勤城市广场';

  console.log('=== 开始测试高德API ===');

  // 0. 测试POI搜索API
  try {
    const poiUrl = `https://restapi.amap.com/v3/place/text?keywords=${encodeURIComponent(testBuilding)}&city=长沙&key=${AMAP_KEY}&extensions=all`;
    console.log('POI搜索URL:', poiUrl);

    const poiResponse = await fetch(poiUrl);
    const poiData = await poiResponse.json();

    console.log('POI搜索返回数据:', JSON.stringify(poiData, null, 2));

    if (poiData.status === '1' && poiData.pois && poiData.pois.length > 0) {
      const poi = poiData.pois[0];
      const location = poi.location.split(',');

      console.log('POI详细信息:', {
        name: poi.name,
        address: poi.address,
        location: poi.location,
        adname: poi.adname,
        cityname: poi.cityname,
        pname: poi.pname,
        type: poi.type
      });

      return {
        success: true,
        method: 'POI搜索',
        coordinates: {
          longitude: parseFloat(location[0]),
          latitude: parseFloat(location[1])
        },
        street: poi.address || '',
        district: poi.adname || '',
        city: poi.cityname || '',
        province: poi.pname || '',
        formatted_address: poi.address || '',
        poi_details: poi
      };
    }
  } catch (error) {
    console.error('POI搜索失败:', error);
  }
  
  // 1. 测试地理编码API
  try {
    const geocodeUrl = `https://restapi.amap.com/v3/geocode/geo?address=${encodeURIComponent('长沙市 ' + testBuilding)}&key=${AMAP_KEY}&extensions=all`;
    console.log('地理编码URL:', geocodeUrl);
    
    const geocodeResponse = await fetch(geocodeUrl);
    const geocodeData = await geocodeResponse.json();
    
    console.log('地理编码返回数据:', JSON.stringify(geocodeData, null, 2));
    
    if (geocodeData.status === '1' && geocodeData.geocodes && geocodeData.geocodes.length > 0) {
      const geocode = geocodeData.geocodes[0];
      const location = geocode.location.split(',');
      const longitude = parseFloat(location[0]);
      const latitude = parseFloat(location[1]);
      
      console.log('获取到坐标:', { longitude, latitude });
      
      // 2. 测试逆地理编码API
      const regeoUrl = `https://restapi.amap.com/v3/geocode/regeo?location=${longitude},${latitude}&key=${AMAP_KEY}&extensions=all&radius=1000`;
      console.log('逆地理编码URL:', regeoUrl);
      
      const regeoResponse = await fetch(regeoUrl);
      const regeoData = await regeoResponse.json();
      
      console.log('逆地理编码返回数据:', JSON.stringify(regeoData, null, 2));
      
      if (regeoData.status === '1' && regeoData.regeocode) {
        const addressComponent = regeoData.regeocode.addressComponent;
        console.log('地址组件:', {
          province: addressComponent.province,
          city: addressComponent.city,
          district: addressComponent.district,
          township: addressComponent.township,
          street: addressComponent.street,
          streetNumber: addressComponent.streetNumber,
          neighborhood: addressComponent.neighborhood,
          building: addressComponent.building
        });
        
        console.log('格式化地址:', regeoData.regeocode.formatted_address);
        
        // 3. 尝试不同的街道字段组合
        const streetOptions = [
          addressComponent.street,
          addressComponent.township,
          addressComponent.streetNumber,
          addressComponent.neighborhood,
          addressComponent.building
        ].filter(Boolean);
        
        console.log('可用的街道信息:', streetOptions);
        
        return {
          success: true,
          coordinates: { longitude, latitude },
          street: streetOptions[0] || '',
          district: addressComponent.district || '',
          city: addressComponent.city || '',
          province: addressComponent.province || '',
          formatted_address: regeoData.regeocode.formatted_address || '',
          all_street_options: streetOptions
        };
      }
    }
  } catch (error) {
    console.error('API测试失败:', error);
    return { success: false, error: error.message };
  }
  
  return { success: false, message: '未获取到有效数据' };
};

// 在浏览器控制台中可以直接调用的测试函数
window.testGeocode = testGeocodeAPI;
