// 高德地理编码API - 增强版，包含街道信息和重试机制
export const geocodeBuilding = async (buildingName, address = '', maxRetries = 2) => {
  const AMAP_KEY = '81ea7b2440e2bddd9f6b540fa04c141d';

  // 定义多种查询策略
  const queryStrategies = [
    address ? `${address} ${buildingName}` : `长沙市 ${buildingName}`,
    `湖南省长沙市 ${buildingName}`,
    `长沙 ${buildingName}`,
    buildingName // 最后尝试只用建筑名称
  ];

  for (let strategyIndex = 0; strategyIndex < queryStrategies.length; strategyIndex++) {
    const query = queryStrategies[strategyIndex];

    for (let retry = 0; retry <= maxRetries; retry++) {
      try {
        const url = `https://restapi.amap.com/v3/geocode/geo?address=${encodeURIComponent(query)}&key=${AMAP_KEY}&extensions=all`;

        console.log(`地理编码查询 (策略${strategyIndex + 1}, 尝试${retry + 1}):`, query);
        const response = await fetch(url);
        const data = await response.json();

        if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
          const geocode = data.geocodes[0];
          const location = geocode.location.split(',');

          const result = {
            longitude: parseFloat(location[0]),
            latitude: parseFloat(location[1]),
            street: geocode.street || geocode.township || '', // 街道信息
            district: geocode.district || '', // 区县信息
            city: geocode.city || '长沙市', // 城市信息
            province: geocode.province || '湖南省', // 省份信息
            formatted_address: geocode.formatted_address || '', // 完整地址
            level: geocode.level || '', // 地址精度级别
            query_used: query // 记录成功的查询策略
          };

          console.log(`地理编码成功: ${buildingName}`, result);
          return result;
        } else {
          console.warn(`地理编码失败 (策略${strategyIndex + 1}, 尝试${retry + 1}):`, data);

          // 如果不是最后一次重试，等待一段时间再重试
          if (retry < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 300 * (retry + 1)));
          }
        }
      } catch (error) {
        console.error(`地理编码请求失败 (策略${strategyIndex + 1}, 尝试${retry + 1}):`, error);

        // 如果不是最后一次重试，等待一段时间再重试
        if (retry < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 300 * (retry + 1)));
        }
      }
    }

    // 策略间的等待时间
    if (strategyIndex < queryStrategies.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }

  console.warn(`所有查询策略都失败: ${buildingName}`);
  return null;
};

const addressHooksData = [
  {
    "name": "德思勤城市广场",
    "longitude": 113.05902,
    "latitude": 28.11799
  },
  {
    "name": "华尔街中心",
    "longitude": 112.986335,
    "latitude": 28.215477
  },
  {
    "name": "华远国际中心",
    "longitude": 112.969975,
    "latitude": 28.189106
  },
  {
    "name": "汇景发展环球中心",
    "longitude": 112.969326,
    "latitude": 28.174152
  },
  {
    "name": "长沙佳兆业广场",
    "longitude": 112.999634,
    "latitude": 28.194831
  },
  {
    "name": "长沙绿地中心T1栋",
    "longitude": 112.987921,
    "latitude": 28.210819
  },
  {
    "name": "润和金融中心",
    "longitude": 112.99982,
    "latitude": 28.18593
  },
  {
    "name": "湖南商会大厦",
    "longitude": 112.986162,
    "latitude": 28.149581
  },
  {
    "name": "万坤图财富广场",
    "longitude": 113.03307,
    "latitude": 28.14415
  },
  {
    "name": "新湖南大厦",
    "longitude": 112.984222,
    "latitude": 28.208940
  },
  {
    "name": "兴业IEC",
    "longitude": 112.985942,
    "latitude": 28.151226
  },
  {
    "name": "御邦国际广场",
    "longitude": 112.979877,
    "latitude": 28.106168
  },
  {
    "name": "运达中央广场A座",
    "longitude": 113.05869,
    "latitude": 28.16767
  },
  {
    "name": "长房国际大厦",
    "longitude": 112.99458,
    "latitude": 28.19094
  },
  {
    "name": "中建广场",
    "longitude": 113.007786,
    "latitude": 28.118282
  },
  {
    "name": "中国石油长沙大厦",
    "longitude": 113.00730,
    "latitude": 28.19599
  },
  {
    "name": "香泽南湖大厦",
    "longitude": 113.010653,
    "latitude": 28.203472
  },
  {
    "name": "北辰A1写字楼",
    "longitude": 112.96974,
    "latitude": 28.23685
  },
  {
    "name": "骏达大厦",
    "longitude": 113.00294,
    "latitude": 28.19231
  },
  {
    "name": "河西王府井写字楼",
    "longitude": 112.94968,
    "latitude": 28.22002
  },
  {
    "name": "红橡国际",
    "longitude": 113.040731,
    "latitude": 28.204342
  },
  {
    "name": "金茂ICC",
    "longitude": 112.90792,
    "latitude": 28.19575
  },
  {
    "name": "长沙西中心",
    "longitude": 112.89862,
    "latitude": 28.22747
  },
  {
    "name": "嘉熙中心",
    "longitude": 112.990819,
    "latitude": 28.185278
  },
  {
    "name": "保利国际广场",
    "longitude": 112.969719,
    "latitude": 28.165343
  },
  {
    "name": "蓝湾国际",
    "longitude": 112.971517,
    "latitude": 28.165492
  },
  {
    "name": "富兴世界金融中心T2",
    "longitude": 112.988073,
    "latitude": 28.215073
  },
  {
    "name": "长沙通程国际大酒店",
    "longitude": 113.00098,
    "latitude": 28.19305
  },
  {
    "name": "天城国际广场",
    "longitude": 112.988697,
    "latitude": 28.118879
  },
  {
    "name": "友阿总部办公大楼",
    "longitude": 112.99470,
    "latitude": 28.18996
  },
  {
    "name": "长房时代国际",
    "longitude": 112.99677,
    "latitude": 28.18929
  },
  {
    "name": "华创国际广场",
    "longitude": 112.988400,
    "latitude": 28.224133
  },
  {
    "name": "长房时代天地",
    "longitude": 112.909279,
    "latitude": 28.217897
  },
  {
    "name": "宇成朝阳广场",
    "longitude": 112.99675,
    "latitude": 28.18963
  },
  {
    "name": "长沙国金中心",
    "longitude": 112.99371,
    "latitude": 28.19533
  },
  {
    "name": "湘江时代广场",
    "longitude": 112.97113,
    "latitude": 28.22270
  },
  {
    "name": "楷林国际C座",
    "longitude": 112.96880,
    "latitude": 28.23517
  },
  {
    "name": "华雅财富大厦",
    "longitude": 113.02458,
    "latitude": 28.13666
  },
  {
    "name": "凯乐微谷",
    "longitude": 112.994872,
    "latitude": 28.238356
  },
  {
    "name": "律政大楼",
    "longitude": 112.93852,
    "latitude": 28.23517
  },
  {
    "name": "湘江集团大厦",
    "longitude": 112.94252,
    "latitude": 28.21942
  },
  {
    "name": "世景国际广场A栋",
    "longitude": 113.097771,
    "latitude": 28.244723
  },
  {
    "name": "万达广场",
    "longitude": 112.98499,
    "latitude": 28.21248
  },
  {
    "name": "湘江财富金融中心A座",
    "longitude": 112.95367,
    "latitude": 28.22997
  },
  {
    "name": "中盈广场",
    "longitude": 113.00655,
    "latitude": 28.19602
  },
  {
    "name": "顺天国际金融中心",
    "longitude": 112.995347,
    "latitude": 28.158386
  },
  {
    "name": "天健壹平方英里-H栋",
    "longitude": 112.94955,
    "latitude": 28.22228
  },
  {
    "name": "旺德府大厦",
    "longitude": 112.99481,
    "latitude": 28.19153
  },
  {
    "name": "运达国际广场",
    "longitude": 112.985112,
    "latitude": 28.203290
  },
  {
    "name": "梅溪湖创新中心",
    "longitude": 112.90841,
    "latitude": 28.19605
  },
  {
    "name": "顺天国际财富中心",
    "longitude": 112.99581,
    "latitude": 28.18801
  },
  {
    "name": "长房视谷中心",
    "longitude": 113.01176,
    "latitude": 28.23355
  },
  {
    "name": "柏宁地王广场",
    "longitude": 112.99555,
    "latitude": 28.18930
  },
  {
    "name": "新长海",
    "longitude": 113.05900,
    "latitude": 28.22107
  },
  {
    "name": "湖南泊富国际广场",
    "longitude": 112.984222,
    "latitude": 28.208940
  },
  {
    "name": "绿地时代广场",
    "longitude": 113.06042,
    "latitude": 28.11862
  },
  {
    "name": "紫鑫中央广场（望城）",
    "longitude": 112.83078,
    "latitude": 28.31648
  },
  {
    "name": "双塔国际广场",
    "longitude": 113.024641,
    "latitude": 28.111992
  },
  {
    "name": "中天广场",
    "longitude": 112.99464,
    "latitude": 28.19015
  },
  {
    "name": "汇金国际",
    "longitude": 112.985412,
    "latitude": 28.162784
  },
  {
    "name": "喜盈门范城",
    "longitude": 113.03183,
    "latitude": 28.14474
  },
  {
    "name": "华晨双帆国际",
    "longitude": 113.028687,
    "latitude": 28.162155
  },
  {
    "name": "壹号座品",
    "longitude": 112.99723,
    "latitude": 28.18974
  },
  {
    "name": "世茂环球金融中心",
    "longitude": 112.97607,
    "latitude": 28.19688
  },
  {
    "name": "湖南投资大厦",
    "longitude": 112.99480,
    "latitude": 28.19064
  },
  {
    "name": "新时空大厦",
    "longitude": 112.985057,
    "latitude": 28.160571
  },
  {
    "name": "复地星光商业广场",
    "longitude": 113.00691,
    "latitude": 28.14321
  },
  {
    "name": "置地广场凤凰中心",
    "longitude": 113.00383,
    "latitude": 28.18952
  },
  {
    "name": "万家丽国际MALL",
    "longitude": 113.03763,
    "latitude": 28.19436
  },
  {
    "name": "银华大厦",
    "longitude": 112.99628,
    "latitude": 28.18945
  },
  {
    "name": "德必岳麓WE",
    "longitude": 112.93704,
    "latitude": 28.22419
  },
  {
    "name": "华美欧大厦",
    "longitude": 113.00035,
    "latitude": 28.19339
  },
  {
    "name": "湘域中央",
    "longitude": 112.99659,
    "latitude": 28.18931
  },
  {
    "name": "华坤时代",
    "longitude": 113.029260,
    "latitude": 28.111860
  },
  {
    "name": "万博汇",
    "longitude": 113.00195,
    "latitude": 28.17948
  },
  {
    "name": "鑫远国际广场A座",
    "longitude": 112.976959,
    "latitude": 28.111509
  },
  {
    "name": "富兴世界金融中心T3",
    "longitude": 112.988073,
    "latitude": 28.215073
  },
  {
    "name": "富兴世界金融中心T6",
    "longitude": 112.988073,
    "latitude": 28.215073
  },
  {
    "name": "湘江财富金融中心CD座",
    "longitude": 112.95341,
    "latitude": 28.23019
  }
];

export default addressHooksData;