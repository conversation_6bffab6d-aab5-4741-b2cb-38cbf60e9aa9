<template>
  <div class="floating-button" @click="openDialog" :title="tooltipText">
    <div class="button-icon">
      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" fill="currentColor"/>
      </svg>
    </div>
    <div class="button-text">批量查询</div>
  </div>
</template>

<script setup>
const emit = defineEmits(['open-dialog']);

// 工具提示文本
const tooltipText = '点击打开写字楼经纬度批量查询工具';

const openDialog = () => {
  emit('open-dialog');
};
</script>

<style scoped>
.floating-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 50%, #03a9f4 100%);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(79, 195, 247, 0.4);
  transition: all 0.3s ease;
  z-index: 1000;
  border: 2px solid rgba(79, 195, 247, 0.3);
}

.floating-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 35px rgba(79, 195, 247, 0.6);
  background: linear-gradient(135deg, #29b6f6 0%, #03a9f4 50%, #0288d1 100%);
}

.floating-button:active {
  transform: translateY(-1px) scale(1.02);
}

.button-icon {
  width: 28px;
  height: 28px;
  color: white;
  margin-bottom: 2px;
}

.button-text {
  color: white;
  font-size: 10px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floating-button {
    width: 60px;
    height: 60px;
    bottom: 20px;
    right: 20px;
  }
  
  .button-icon {
    width: 20px;
    height: 20px;
  }
  
  .button-text {
    font-size: 8px;
  }
}

/* 添加脉冲动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.4);
  }
  50% {
    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.6), 0 0 0 10px rgba(79, 195, 247, 0.1);
  }
  100% {
    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.4);
  }
}

.floating-button {
  animation: pulse 2s infinite;
}

.floating-button:hover {
  animation: none;
}
</style>
