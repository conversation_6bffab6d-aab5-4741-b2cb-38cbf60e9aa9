<template>
  <div class="test-container">
    <h2>地理编码测试页面</h2>
    
    <div class="test-section">
      <h3>单个查询测试</h3>
      <div class="input-group">
        <input 
          v-model="testBuildingName" 
          placeholder="输入写字楼名称" 
          class="test-input"
        />
        <button @click="testSingleQuery" class="test-btn" :disabled="isQuerying">
          {{ isQuerying ? '查询中...' : '查询' }}
        </button>
      </div>
      
      <div v-if="singleResult" class="result">
        <h4>查询结果：</h4>
        <pre>{{ JSON.stringify(singleResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>悬浮按钮和弹窗测试</h3>
      <button @click="openDialog" class="test-btn">打开地理编码弹窗</button>
    </div>

    <!-- 地理编码弹窗 -->
    <GeocodeDialog 
      :visible="dialogVisible" 
      @close="closeDialog" 
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { geocodeBuilding } from '@/net/addressHooks.js';
import GeocodeDialog from '@/components/common/GeocodeDialog.vue';

// 测试数据
const testBuildingName = ref('德思勤城市广场');
const isQuerying = ref(false);
const singleResult = ref(null);
const dialogVisible = ref(false);

// 单个查询测试
const testSingleQuery = async () => {
  if (!testBuildingName.value.trim()) {
    alert('请输入写字楼名称');
    return;
  }

  isQuerying.value = true;
  singleResult.value = null;

  try {
    const result = await geocodeBuilding(testBuildingName.value.trim());
    singleResult.value = {
      name: testBuildingName.value.trim(),
      result: result,
      success: !!result,
      // 展示详细信息
      details: result ? {
        longitude: result.longitude,
        latitude: result.latitude,
        street: result.street,
        district: result.district,
        city: result.city,
        formatted_address: result.formatted_address,
        level: result.level
      } : null
    };
  } catch (error) {
    singleResult.value = {
      name: testBuildingName.value.trim(),
      error: error.message,
      success: false,
      details: null
    };
  } finally {
    isQuerying.value = false;
  }
};

// 弹窗控制
const openDialog = () => {
  dialogVisible.value = true;
};

const closeDialog = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  color: white;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 8px;
  background: rgba(0, 20, 40, 0.5);
}

.input-group {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.test-input {
  flex: 1;
  padding: 10px;
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 4px;
  background: rgba(0, 20, 40, 0.5);
  color: white;
}

.test-input:focus {
  outline: none;
  border-color: #4fc3f7;
}

.test-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background: linear-gradient(135deg, #4fc3f7, #29b6f6);
  color: white;
  cursor: pointer;
  font-weight: bold;
}

.test-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #29b6f6, #03a9f4);
}

.test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.result {
  margin-top: 15px;
  padding: 15px;
  background: rgba(0, 30, 60, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(79, 195, 247, 0.2);
}

.result pre {
  color: #4fc3f7;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
}

h2, h3, h4 {
  color: #4fc3f7;
  margin-bottom: 15px;
}
</style>
