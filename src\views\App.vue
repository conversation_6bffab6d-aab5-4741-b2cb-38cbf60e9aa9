<template>
  <div class="app-wrapper">
    <AppContainer>
      <!-- 左侧内容插槽 -->
      <template #header-left>
        <component :is="currentHeaderLeft" v-bind="headerLeftProps" />
      </template>

      <!-- 右侧内容插槽 -->
      <template #header-right>
        <component :is="currentHeaderRight" v-bind="headerRightProps" />
      </template>

      <!-- 页面内容插槽 -->
      <template #content>
        <transition :name="transitionName" mode="out-in">
          <router-view :key="currentRoute" />
        </transition>
      </template>
    </AppContainer>

    <!-- 悬浮按钮 -->
    <FloatingButton @open-dialog="openGeocodeDialog" />

    <!-- 地理编码弹窗 -->
    <GeocodeDialog
      :visible="geocodeDialogVisible"
      @close="closeGeocodeDialog"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import AppContainer from '@/components/layout/AppContainer.vue';

// 不再需要导入页面组件，使用router-view

// 导入头部组件
import IndexHeaderLeft from '@/components/header/IndexHeaderLeft.vue';
import DistrictHeaderLeft from '@/components/header/DistrictHeaderLeft.vue';
import BuildingHeaderLeft from '@/components/header/BuildingHeaderLeft.vue';
import DefaultHeaderRight from '@/components/header/DefaultHeaderRight.vue';

// 导入悬浮按钮和弹窗组件
import FloatingButton from '@/components/common/FloatingButton.vue';
import GeocodeDialog from '@/components/common/GeocodeDialog.vue';

const route = useRoute();
const router = useRouter();

// 过渡动画名称
const transitionName = ref('fade');

// 地理编码弹窗状态
const geocodeDialogVisible = ref(false);

// 当前左侧头部组件
const currentHeaderLeft = computed(() => {
  const path = route.path || '/';
  if (path === '/') {
    return IndexHeaderLeft;
  } else if (path.startsWith('/district/')) {
    return DistrictHeaderLeft;
  } else if (path.startsWith('/building/')) {
    return BuildingHeaderLeft;
  }
  return IndexHeaderLeft;
});

// 当前右侧头部组件
const currentHeaderRight = computed(() => {
  return DefaultHeaderRight;
});

// 头部左侧属性
const headerLeftProps = computed(() => {
  return {
    route: route,
    router: router
  };
});

// 头部右侧属性
const headerRightProps = computed(() => {
  return {};
});

// 弹窗控制方法
const openGeocodeDialog = () => {
  geocodeDialogVisible.value = true;
};

const closeGeocodeDialog = () => {
  geocodeDialogVisible.value = false;
};

// 监听路由变化
watch(() => route.path, (newPath, oldPath) => {
  console.log('App.vue - 路由变化:', { oldPath, newPath, params: route.params, query: route.query });

  // 设置过渡动画 - 统一使用淡入淡出效果
  transitionName.value = 'fade';
}, { immediate: true });
</script>

<style scoped lang="less">
.app-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 过渡动画 - 优化的淡入淡出效果 */
.fade-enter-active {
  transition: opacity 0.5s ease-in;
}

.fade-leave-active {
  transition: opacity 0.3s ease-out;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 移除滑动动画，统一使用淡入淡出效果 */
</style>
