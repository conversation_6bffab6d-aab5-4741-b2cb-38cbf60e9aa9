// 测试街道信息提取功能
export const testStreetExtraction = () => {
  // 从地址字符串中提取街道信息
  const extractStreetFromAddress = (address) => {
    if (!address) return '';
    
    console.log('正在处理地址:', address);
    
    // 常见的街道关键词
    const streetKeywords = ['路', '街', '大道', '巷', '弄', '里', '道', '广场', '中心'];
    
    // 尝试匹配街道模式
    for (const keyword of streetKeywords) {
      const regex = new RegExp(`([^区县市]{2,10}${keyword})`, 'g');
      const matches = address.match(regex);
      if (matches && matches.length > 0) {
        console.log(`匹配到街道关键词 "${keyword}":`, matches[0]);
        return matches[0];
      }
    }
    
    // 如果没有匹配到，尝试提取区县后面的部分
    const districtMatch = address.match(/([^市]{2,4}[区县])(.+)/);
    if (districtMatch && districtMatch[2]) {
      const afterDistrict = districtMatch[2];
      console.log('区县后面的部分:', afterDistrict);
      // 提取前面的部分作为街道
      const streetMatch = afterDistrict.match(/^([^0-9]{2,15})/);
      if (streetMatch) {
        console.log('提取的街道信息:', streetMatch[1].trim());
        return streetMatch[1].trim();
      }
    }
    
    return '';
  };

  // 测试用例
  const testAddresses = [
    '湖南省长沙市雨花区劳动东路238号',
    '长沙市天心区湘府中路258号',
    '湖南省长沙市岳麓区银盆南路361号',
    '长沙市开福区湘江中路万达广场',
    '雨花区韶山南路123号德思勤城市广场',
    '天心区解放西路188号华远国际中心',
    '岳麓区桐梓坡西路229号麓谷国际工业园',
    '开福区芙蓉中路一段478号运达国际广场'
  ];

  console.log('=== 开始测试街道信息提取 ===');
  
  const results = testAddresses.map(address => {
    const street = extractStreetFromAddress(address);
    const result = {
      original: address,
      extracted_street: street,
      success: !!street
    };
    console.log('测试结果:', result);
    return result;
  });

  console.log('=== 测试完成 ===');
  console.log('总结:', {
    total: results.length,
    success: results.filter(r => r.success).length,
    failed: results.filter(r => !r.success).length,
    success_rate: `${Math.round((results.filter(r => r.success).length / results.length) * 100)}%`
  });

  return results;
};

// 在浏览器控制台中可以直接调用
window.testStreetExtraction = testStreetExtraction;
