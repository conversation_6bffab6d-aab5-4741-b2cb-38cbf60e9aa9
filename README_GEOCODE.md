# 写字楼经纬度批量查询功能

## 功能概述

本功能提供了一个悬浮按钮和弹窗界面，用于批量查询写字楼的经纬度坐标。使用高德地图API进行地理编码查询。

## 功能特点

### 1. 悬浮按钮
- 位置：页面右下角
- 样式：圆形蓝色渐变按钮，带有地图图标
- 动画：脉冲动画效果，悬停时停止动画并放大
- 响应式：在移动设备上自动调整大小

### 2. 批量查询弹窗
- **输入区域**：支持多行文本输入，每行一个写字楼名称
- **示例数据**：点击"加载示例数据"按钮可快速填入测试数据
- **进度显示**：实时显示查询进度和百分比
- **结果展示**：表格形式显示查询结果，包括成功和失败状态
- **数据导出**：支持JSON格式导出和剪贴板复制

### 3. 高德API集成
- API密钥：`81ea7b2440e2bddd9f6b540fa04c141d`
- 地理编码接口：`https://restapi.amap.com/v3/geocode/geo`
- 查询策略：优先使用地址+建筑名称，fallback到城市+建筑名称
- 限流控制：每次查询间隔200ms，避免API限制

## 文件结构

```
src/
├── components/
│   └── common/
│       ├── FloatingButton.vue     # 悬浮按钮组件
│       └── GeocodeDialog.vue      # 地理编码弹窗组件
├── net/
│   └── addressHooks.js            # 地理编码API封装
└── views/
    └── App.vue                    # 主应用容器（已集成组件）
```

## 使用方法

### 1. 基本使用
1. 点击页面右下角的悬浮按钮
2. 在弹窗中输入写字楼名称（每行一个）
3. 点击"开始批量查询"按钮
4. 等待查询完成，查看结果

### 2. 示例数据测试
1. 点击"加载示例数据"按钮
2. 自动填入10个长沙写字楼名称
3. 点击"开始批量查询"进行测试

### 3. 结果处理
- **导出JSON**：点击"导出结果"下载JSON文件
- **复制数据**：点击"复制结果"复制到剪贴板（制表符分隔格式）
- **清空重置**：点击"清空结果"重置所有数据

## API函数

### geocodeBuilding(buildingName, address)
```javascript
import { geocodeBuilding } from '@/net/addressHooks.js';

// 基本用法
const result = await geocodeBuilding('德思勤城市广场');
// 返回: { longitude: 113.05902, latitude: 28.11799 } 或 null

// 带地址的用法
const result = await geocodeBuilding('德思勤城市广场', '长沙市雨花区');
```

## 组件使用

### FloatingButton
```vue
<template>
  <FloatingButton @open-dialog="handleOpenDialog" />
</template>

<script setup>
import FloatingButton from '@/components/common/FloatingButton.vue';

const handleOpenDialog = () => {
  // 处理打开弹窗事件
};
</script>
```

### GeocodeDialog
```vue
<template>
  <GeocodeDialog 
    :visible="dialogVisible" 
    @close="handleCloseDialog" 
  />
</template>

<script setup>
import { ref } from 'vue';
import GeocodeDialog from '@/components/common/GeocodeDialog.vue';

const dialogVisible = ref(false);

const handleCloseDialog = () => {
  dialogVisible.value = false;
};
</script>
```

## 样式特点

### 设计风格
- 深色主题，符合项目整体风格
- 蓝色渐变色彩方案（#4fc3f7系列）
- 圆角边框和阴影效果
- 响应式设计，支持多种屏幕尺寸

### 动画效果
- 悬浮按钮脉冲动画
- 按钮悬停放大效果
- 进度条平滑过渡
- 弹窗淡入淡出效果

## 测试页面

访问 `http://localhost:8081/#/test` 可以进入测试页面，包含：
- 单个查询测试
- 弹窗功能测试
- 实时结果显示

## 注意事项

1. **API限制**：高德API有调用频率限制，建议合理控制查询频率
2. **网络依赖**：需要网络连接才能进行地理编码查询
3. **浏览器兼容**：使用了现代浏览器API（如clipboard），需要HTTPS或localhost环境
4. **错误处理**：查询失败的项目会在结果中标记为失败状态

## 扩展建议

1. **缓存机制**：可以添加本地缓存避免重复查询
2. **批量导入**：支持Excel/CSV文件导入
3. **地图预览**：在结果中添加地图预览功能
4. **历史记录**：保存查询历史记录
5. **API配置**：支持切换不同的地图API服务商
